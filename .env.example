# Application Configuration
APP_NAME=Cerebro API
APP_VERSION=1.0.0
DEBUG=false
API_V1_STR=/api/v1

# Security
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_MINUTES=10080

# CORS
BACKEND_CORS_ORIGINS=["http://localhost:3000","http://localhost:8080"]

# Database - PostgreSQL
POSTGRES_SERVER=localhost
POSTGRES_USER=cerebro_user
POSTGRES_PASSWORD=cerebro_password
POSTGRES_DB=cerebro
POSTGRES_PORT=5432
DATABASE_URL=postgresql+asyncpg://cerebro_user:cerebro_password@localhost:5432/cerebro

# MongoDB
MONGODB_URL=***************************************************************
MONGODB_DB_NAME=cerebro

# Redis
REDIS_URL=redis://:cerebro_password@localhost:6379/0
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=cerebro_password
REDIS_DB=0

# RabbitMQ
RABBITMQ_URL=amqp://cerebro_user:cerebro_password@localhost:5672/cerebro
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672
RABBITMQ_USER=cerebro_user
RABBITMQ_PASSWORD=cerebro_password
RABBITMQ_VHOST=cerebro

# Inngest
INNGEST_URL=http://localhost:8288
INNGEST_EVENT_KEY=local
INNGEST_SIGNING_KEY=signkey-test-12345678901234567890123456789012

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_PERIOD=60

# Email Configuration
SMTP_TLS=true
SMTP_PORT=587
SMTP_HOST=smtp.gmail.com
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=Cerebro API

# Initial Superuser
FIRST_SUPERUSER_EMAIL=<EMAIL>
FIRST_SUPERUSER_PASSWORD=admin123

# Credentials
CREDENTIAL_MASTER_KEY=your-secret-key-change-this-in-production
