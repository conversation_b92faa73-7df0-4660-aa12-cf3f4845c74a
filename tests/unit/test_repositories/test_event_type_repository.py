"""
Unit tests for EventTypeRepository.
"""

import pytest
from datetime import datetime, timezone
from uuid import uuid4
from unittest.mock import AsyncMock, MagicMock, patch

from bson import ObjectId
from pymongo.errors import Duplicate<PERSON>eyError

from app.repositories.event_type_repository import EventTypeRepository
from app.schemas.event_type import EventTypeCreate, EventTypeUpdate
from app.utils.exceptions import ConflictError, DatabaseError


@pytest.fixture
def mock_db():
    """Mock MongoDB database."""
    db = MagicMock()
    collection = AsyncMock()
    db.event_type = collection
    return db


@pytest.fixture
def repository(mock_db):
    """EventTypeRepository instance with mocked database."""
    return EventTypeRepository(mock_db)


@pytest.fixture
def sample_event_type_create():
    """Sample event type creation data."""
    return EventTypeCreate(
        name="user_signup",
        description="User signup event",
        schema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string"},
                "email": {"type": "string", "format": "email"}
            },
            "required": ["user_id", "email"]
        },
        created_by=uuid4()
    )


@pytest.fixture
def sample_event_type_update():
    """Sample event type update data."""
    return EventTypeUpdate(
        name="user_signup_updated",
        description="Updated user signup event",
        schema={
            "type": "object",
            "properties": {
                "user_id": {"type": "string"},
                "email": {"type": "string", "format": "email"},
                "timestamp": {"type": "string", "format": "date-time"}
            },
            "required": ["user_id", "email", "timestamp"]
        },
        edited_by=uuid4()
    )


class TestEventTypeRepository:
    """Test cases for EventTypeRepository."""
    
    @pytest.mark.asyncio
    async def test_ensure_indexes(self, repository, mock_db):
        """Test index creation."""
        mock_db.event_type.create_indexes = AsyncMock()
        
        await repository.ensure_indexes()
        
        mock_db.event_type.create_indexes.assert_called_once()
        # Verify the indexes being created
        call_args = mock_db.event_type.create_indexes.call_args[0][0]
        assert len(call_args) == 5  # 5 indexes should be created
    
    @pytest.mark.asyncio
    async def test_create_success(self, repository, mock_db, sample_event_type_create):
        """Test successful event type creation."""
        # Mock successful insertion
        mock_result = MagicMock()
        mock_result.inserted_id = ObjectId()
        mock_db.event_type.insert_one = AsyncMock(return_value=mock_result)
        
        # Mock document retrieval
        created_doc = {
            "_id": mock_result.inserted_id,
            "name": sample_event_type_create.name,
            "description": sample_event_type_create.description,
            "schema": sample_event_type_create.schema,
            "version": sample_event_type_create.version,
            "created_by": sample_event_type_create.created_by,
            "edited_by": None,
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        mock_db.event_type.find_one = AsyncMock(return_value=created_doc)
        
        result = await repository.create(sample_event_type_create)
        
        assert result["id"] == str(mock_result.inserted_id)
        assert result["name"] == sample_event_type_create.name
        assert "_id" not in result
        mock_db.event_type.insert_one.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_duplicate_name(self, repository, mock_db, sample_event_type_create):
        """Test creation with duplicate name."""
        mock_db.event_type.insert_one = AsyncMock(side_effect=DuplicateKeyError("duplicate key"))
        
        with pytest.raises(ConflictError) as exc_info:
            await repository.create(sample_event_type_create)
        
        assert "already exists" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_by_id_success(self, repository, mock_db):
        """Test successful retrieval by ID."""
        object_id = ObjectId()
        mock_doc = {
            "_id": object_id,
            "name": "test_event",
            "description": "Test event",
            "schema": {"type": "object"},
            "version": 1,
            "created_by": uuid4(),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        mock_db.event_type.find_one = AsyncMock(return_value=mock_doc)
        
        result = await repository.get_by_id(str(object_id))
        
        assert result["id"] == str(object_id)
        assert result["name"] == "test_event"
        assert "_id" not in result
        mock_db.event_type.find_one.assert_called_once_with({"_id": object_id})
    
    @pytest.mark.asyncio
    async def test_get_by_id_not_found(self, repository, mock_db):
        """Test retrieval by ID when not found."""
        mock_db.event_type.find_one = AsyncMock(return_value=None)
        
        result = await repository.get_by_id(str(ObjectId()))
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_get_by_id_invalid_id(self, repository, mock_db):
        """Test retrieval with invalid ObjectId."""
        result = await repository.get_by_id("invalid_id")
        
        assert result is None
        mock_db.event_type.find_one.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_get_by_name_success(self, repository, mock_db):
        """Test successful retrieval by name."""
        object_id = ObjectId()
        mock_doc = {
            "_id": object_id,
            "name": "test_event",
            "description": "Test event",
            "schema": {"type": "object"},
            "version": 1,
            "created_by": uuid4(),
            "created_at": datetime.now(timezone.utc),
            "updated_at": datetime.now(timezone.utc)
        }
        mock_db.event_type.find_one = AsyncMock(return_value=mock_doc)
        
        result = await repository.get_by_name("test_event")
        
        assert result["id"] == str(object_id)
        assert result["name"] == "test_event"
        mock_db.event_type.find_one.assert_called_once_with({"name": "test_event"})
    
    @pytest.mark.asyncio
    async def test_get_multi_success(self, repository, mock_db):
        """Test successful retrieval of multiple documents."""
        object_ids = [ObjectId(), ObjectId()]
        mock_docs = [
            {
                "_id": object_ids[0],
                "name": "event1",
                "description": "Event 1",
                "created_at": datetime.now(timezone.utc)
            },
            {
                "_id": object_ids[1],
                "name": "event2",
                "description": "Event 2",
                "created_at": datetime.now(timezone.utc)
            }
        ]
        
        # Mock cursor
        mock_cursor = AsyncMock()
        mock_cursor.sort.return_value = mock_cursor
        mock_cursor.skip.return_value = mock_cursor
        mock_cursor.limit.return_value = mock_cursor
        mock_cursor.to_list = AsyncMock(return_value=mock_docs)
        mock_db.event_type.find.return_value = mock_cursor
        
        result = await repository.get_multi(skip=0, limit=10)
        
        assert len(result) == 2
        assert result[0]["id"] == str(object_ids[0])
        assert result[1]["id"] == str(object_ids[1])
        assert "_id" not in result[0]
        assert "_id" not in result[1]
    
    @pytest.mark.asyncio
    async def test_count_success(self, repository, mock_db):
        """Test successful count operation."""
        mock_db.event_type.count_documents = AsyncMock(return_value=5)
        
        result = await repository.count()
        
        assert result == 5
        mock_db.event_type.count_documents.assert_called_once_with({})
    
    @pytest.mark.asyncio
    async def test_update_success(self, repository, mock_db, sample_event_type_update):
        """Test successful update operation."""
        object_id = ObjectId()
        updated_doc = {
            "_id": object_id,
            "name": sample_event_type_update.name,
            "description": sample_event_type_update.description,
            "schema": sample_event_type_update.schema,
            "version": 2,  # Incremented
            "edited_by": sample_event_type_update.edited_by,
            "updated_at": datetime.now(timezone.utc)
        }
        
        mock_db.event_type.find_one_and_update = AsyncMock(return_value=updated_doc)
        
        result = await repository.update(str(object_id), sample_event_type_update)
        
        assert result["id"] == str(object_id)
        assert result["name"] == sample_event_type_update.name
        assert result["version"] == 2
        assert "_id" not in result
    
    @pytest.mark.asyncio
    async def test_update_not_found(self, repository, mock_db, sample_event_type_update):
        """Test update when document not found."""
        mock_db.event_type.find_one_and_update = AsyncMock(return_value=None)
        
        result = await repository.update(str(ObjectId()), sample_event_type_update)
        
        assert result is None
    
    @pytest.mark.asyncio
    async def test_delete_success(self, repository, mock_db):
        """Test successful deletion."""
        mock_result = MagicMock()
        mock_result.deleted_count = 1
        mock_db.event_type.delete_one = AsyncMock(return_value=mock_result)
        
        result = await repository.delete(str(ObjectId()))
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_delete_not_found(self, repository, mock_db):
        """Test deletion when document not found."""
        mock_result = MagicMock()
        mock_result.deleted_count = 0
        mock_db.event_type.delete_one = AsyncMock(return_value=mock_result)
        
        result = await repository.delete(str(ObjectId()))
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_exists_true(self, repository, mock_db):
        """Test exists check when document exists."""
        mock_db.event_type.count_documents = AsyncMock(return_value=1)
        
        result = await repository.exists(str(ObjectId()))
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_exists_false(self, repository, mock_db):
        """Test exists check when document doesn't exist."""
        mock_db.event_type.count_documents = AsyncMock(return_value=0)
        
        result = await repository.exists(str(ObjectId()))
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_database_error_handling(self, repository, mock_db, sample_event_type_create):
        """Test database error handling."""
        mock_db.event_type.insert_one = AsyncMock(side_effect=Exception("Database connection failed"))
        
        with pytest.raises(DatabaseError) as exc_info:
            await repository.create(sample_event_type_create)
        
        assert "Failed to create event type" in str(exc_info.value)
