"""
Unit tests for ValidationService.
"""

import pytest
from app.services.validation_service import ValidationService
from app.schemas.event_type import JSONSchema
from app.utils.exceptions import ValidationError


class TestValidationService:
    """Test cases for ValidationService."""
    
    def test_validate_schema_definition_valid(self):
        """Test validation of valid schema definitions."""
        # Valid object schema as dict
        schema_dict = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer", "minimum": 0}
            },
            "required": ["name"]
        }

        # Should not raise exception
        ValidationService.validate_schema_definition(schema_dict)

        # Valid object schema as JSONSchema object
        schema_obj = JSONSchema(
            type="object",
            properties={
                "name": {"type": "string"},
                "age": {"type": "integer", "minimum": 0}
            },
            required=["name"]
        )

        # Should not raise exception
        ValidationService.validate_schema_definition(schema_obj)
    
    def test_validate_schema_definition_invalid(self):
        """Test validation of invalid schema definitions."""
        # Invalid schema - invalid type value
        invalid_schema = {
            "type": "invalid_type_name",
            "properties": {
                "name": {"type": "string"}
            }
        }

        with pytest.raises(ValidationError):
            ValidationService.validate_schema_definition(invalid_schema)
    
    def test_validate_data_against_schema_valid(self):
        """Test validation of valid data against schema."""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer", "minimum": 0}
            },
            "required": ["name"]
        }
        
        valid_data = {"name": "John", "age": 30}
        
        result = ValidationService.validate_data_against_schema(valid_data, schema)
        
        assert result.valid is True
        assert len(result.errors) == 0
    
    def test_validate_data_against_schema_invalid(self):
        """Test validation of invalid data against schema."""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer", "minimum": 0}
            },
            "required": ["name"]
        }
        
        # Missing required field
        invalid_data = {"age": 30}
        
        result = ValidationService.validate_data_against_schema(invalid_data, schema)
        
        assert result.valid is False
        assert len(result.errors) == 1
        assert result.errors[0].field == "root"
        assert "name" in result.errors[0].message
    
    def test_validate_data_type_mismatch(self):
        """Test validation with type mismatches."""
        schema = {
            "type": "object",
            "properties": {
                "age": {"type": "integer"}
            }
        }
        
        # Wrong type
        invalid_data = {"age": "thirty"}
        
        result = ValidationService.validate_data_against_schema(invalid_data, schema)
        
        assert result.valid is False
        assert len(result.errors) == 1
        assert result.errors[0].field == "age"
        assert "integer" in result.errors[0].message
        assert "str" in result.errors[0].message
    
    def test_validate_array_schema(self):
        """Test validation of array schemas."""
        schema = {
            "type": "array",
            "items": {"type": "string"},
            "minItems": 1,
            "maxItems": 3
        }
        
        # Valid array
        valid_data = ["item1", "item2"]
        result = ValidationService.validate_data_against_schema(valid_data, schema)
        assert result.valid is True
        
        # Invalid - too many items
        invalid_data = ["item1", "item2", "item3", "item4"]
        result = ValidationService.validate_data_against_schema(invalid_data, schema)
        assert result.valid is False
        assert any("at most 3" in error.message for error in result.errors)
    
    def test_validate_nested_objects(self):
        """Test validation of nested object structures."""
        schema = {
            "type": "object",
            "properties": {
                "user": {
                    "type": "object",
                    "properties": {
                        "name": {"type": "string"},
                        "contacts": {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "properties": {
                                    "type": {"type": "string", "enum": ["email", "phone"]},
                                    "value": {"type": "string"}
                                },
                                "required": ["type", "value"]
                            }
                        }
                    },
                    "required": ["name"]
                }
            },
            "required": ["user"]
        }
        
        # Valid nested data
        valid_data = {
            "user": {
                "name": "John",
                "contacts": [
                    {"type": "email", "value": "<EMAIL>"},
                    {"type": "phone", "value": "************"}
                ]
            }
        }
        
        result = ValidationService.validate_data_against_schema(valid_data, schema)
        assert result.valid is True
        
        # Invalid nested data - missing required field in array item
        invalid_data = {
            "user": {
                "name": "John",
                "contacts": [
                    {"type": "email"}  # Missing 'value'
                ]
            }
        }
        
        result = ValidationService.validate_data_against_schema(invalid_data, schema)
        assert result.valid is False
        assert any("user.contacts[0]" in error.field for error in result.errors)
    
    def test_build_field_path(self):
        """Test field path building for nested structures."""
        # Test simple field
        path = ValidationService._build_field_path(["name"])
        assert path == "name"
        
        # Test nested object field
        path = ValidationService._build_field_path(["user", "name"])
        assert path == "user.name"
        
        # Test array index
        path = ValidationService._build_field_path(["items", 0, "name"])
        assert path == "items[0].name"
        
        # Test root
        path = ValidationService._build_field_path([])
        assert path == "root"
    
    def test_format_error_messages(self):
        """Test custom error message formatting."""
        schema = {
            "type": "object",
            "properties": {
                "email": {"type": "string", "format": "email"},
                "age": {"type": "integer", "minimum": 18, "maximum": 100},
                "status": {"type": "string", "enum": ["active", "inactive"]},
                "name": {"type": "string", "minLength": 2, "maxLength": 50}
            },
            "required": ["email"]
        }
        
        # Test various validation errors
        test_cases = [
            ({"age": 15}, "greater than or equal to 18"),
            ({"age": 150}, "less than or equal to 100"),
            ({"status": "unknown"}, "must be one of: active, inactive"),
            ({"name": "a"}, "at least 2 characters"),
            ({"name": "a" * 60}, "at most 50 characters"),
            ({}, "Required property")  # Simplified check since the exact format varies
        ]
        
        for invalid_data, expected_message in test_cases:
            result = ValidationService.validate_data_against_schema(invalid_data, schema)
            assert result.valid is False
            assert any(expected_message in error.message for error in result.errors)
    
    def test_get_schema_summary(self):
        """Test schema summary generation."""
        schema = {
            "type": "object",
            "description": "User profile schema",
            "properties": {
                "name": {"type": "string"},
                "age": {"type": "integer"},
                "email": {"type": "string"},
                "active": {"type": "boolean"}
            },
            "required": ["name", "email"]
        }
        
        summary = ValidationService.get_schema_summary(schema)
        
        assert summary["type"] == "object"
        assert summary["description"] == "User profile schema"
        assert set(summary["required_fields"]) == {"name", "email"}
        assert set(summary["optional_fields"]) == {"age", "active"}
        assert summary["field_types"]["name"] == "string"
        assert summary["field_types"]["age"] == "integer"
    
    def test_validate_and_get_detailed_report(self):
        """Test detailed validation report generation."""
        schema = {
            "type": "object",
            "properties": {
                "name": {"type": "string"}
            },
            "required": ["name"]
        }
        
        data = {"name": "John"}
        
        report = ValidationService.validate_and_get_detailed_report(data, schema)
        
        assert "validation_result" in report
        assert "schema_summary" in report
        assert "data_type" in report
        assert "data_size" in report
        
        assert report["validation_result"]["valid"] is True
        assert report["data_type"] == "dict"
        assert report["data_size"] > 0
