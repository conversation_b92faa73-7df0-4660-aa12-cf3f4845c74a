"""
API endpoint tests for event types.
"""

import pytest
from unittest.mock import AsyncMock, patch
from uuid import uuid4

from fastapi import status
from httpx import AsyncClient

from app.schemas.event_type import EventType, ValidationResponse


@pytest.fixture
def sample_event_type_data():
    """Sample event type creation data."""
    return {
        "name": "user_signup",
        "description": "User signup event",
        "schema": {
            "type": "object",
            "properties": {
                "user_id": {"type": "string"},
                "email": {"type": "string", "format": "email"}
            },
            "required": ["user_id", "email"]
        }
    }


@pytest.fixture
def sample_event_type_response():
    """Sample event type response."""
    return {
        "id": "507f1f77bcf86cd799439011",
        "name": "user_signup",
        "description": "User signup event",
        "schema": {
            "type": "object",
            "properties": {
                "user_id": {"type": "string"},
                "email": {"type": "string", "format": "email"}
            },
            "required": ["user_id", "email"]
        },
        "version": 1,
        "created_by": str(uuid4()),
        "edited_by": None,
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
    }


class TestEventTypeEndpoints:
    """Test cases for event type API endpoints."""
    
    @pytest.mark.asyncio
    async def test_create_event_type_success(self, async_client: AsyncClient, auth_headers, sample_event_type_data, sample_event_type_response):
        """Test successful event type creation."""
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.create_event_type.return_value = EventType(**sample_event_type_response)
            
            response = await async_client.post(
                "/api/v1/event-types",
                json=sample_event_type_data,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_201_CREATED
            data = response.json()
            assert data["status"] is True
            assert data["message"] == "Event type created successfully"
            assert data["data"]["name"] == sample_event_type_data["name"]
    
    @pytest.mark.asyncio
    async def test_create_event_type_invalid_schema(self, async_client: AsyncClient, auth_headers):
        """Test creation with invalid schema."""
        invalid_data = {
            "name": "invalid_event",
            "description": "Invalid event",
            "schema": {
                "properties": {"name": {"type": "string"}}
                # Missing required "type" field
            }
        }
        
        response = await async_client.post(
            "/api/v1/event-types",
            json=invalid_data,
            headers=auth_headers
        )
        
        assert response.status_code == status.HTTP_422_UNPROCESSABLE_ENTITY
        data = response.json()
        assert "Schema must have a \"type\" field" in str(data)
    
    @pytest.mark.asyncio
    async def test_get_event_types_success(self, async_client: AsyncClient, auth_headers):
        """Test successful retrieval of event types."""
        mock_event_types = [
            {
                "id": "1",
                "name": "event1",
                "description": "Event 1",
                "version": 1,
                "created_by": str(uuid4()),
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            },
            {
                "id": "2",
                "name": "event2",
                "description": "Event 2",
                "version": 1,
                "created_by": str(uuid4()),
                "created_at": "2024-01-01T00:00:00Z",
                "updated_at": "2024-01-01T00:00:00Z"
            }
        ]
        
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.get_event_types.return_value = mock_event_types
            mock_service_instance.count_event_types.return_value = 2
            
            response = await async_client.get(
                "/api/v1/event-types?skip=0&limit=10",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] is True
            assert len(data["data"]["items"]) == 2
            assert data["data"]["total"] == 2
    
    @pytest.mark.asyncio
    async def test_get_event_type_by_id_success(self, async_client: AsyncClient, auth_headers, sample_event_type_response):
        """Test successful retrieval by ID."""
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.get_event_type_by_id.return_value = EventType(**sample_event_type_response)
            
            response = await async_client.get(
                "/api/v1/event-types/507f1f77bcf86cd799439011",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] is True
            assert data["data"]["id"] == "507f1f77bcf86cd799439011"
    
    @pytest.mark.asyncio
    async def test_get_event_type_by_id_not_found(self, async_client: AsyncClient, auth_headers):
        """Test retrieval by ID when not found."""
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.get_event_type_by_id.side_effect = Exception("Event type not found")
            
            response = await async_client.get(
                "/api/v1/event-types/507f1f77bcf86cd799439011",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] is False
            assert data["error_code"] == "HTTP_ERROR"
    
    @pytest.mark.asyncio
    async def test_update_event_type_success(self, async_client: AsyncClient, auth_headers, sample_event_type_response):
        """Test successful event type update."""
        update_data = {
            "name": "updated_event",
            "description": "Updated description"
        }
        
        updated_response = sample_event_type_response.copy()
        updated_response.update(update_data)
        updated_response["version"] = 2
        
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.update_event_type.return_value = EventType(**updated_response)
            
            response = await async_client.put(
                "/api/v1/event-types/507f1f77bcf86cd799439011",
                json=update_data,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] is True
            assert data["data"]["name"] == "updated_event"
            assert data["data"]["version"] == 2
    
    @pytest.mark.asyncio
    async def test_delete_event_type_success(self, async_client: AsyncClient, auth_headers):
        """Test successful event type deletion."""
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.delete_event_type.return_value = True
            
            response = await async_client.delete(
                "/api/v1/event-types/507f1f77bcf86cd799439011",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] is True
            assert data["message"] == "Event type deleted successfully"
            assert data["data"]["id"] == "507f1f77bcf86cd799439011"
    
    @pytest.mark.asyncio
    async def test_validate_data_success(self, async_client: AsyncClient, auth_headers):
        """Test successful data validation."""
        validation_data = {
            "data": {
                "user_id": "123",
                "email": "<EMAIL>"
            }
        }
        
        validation_response = ValidationResponse(valid=True, errors=[])
        
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.validate_data.return_value = validation_response
            
            response = await async_client.post(
                "/api/v1/event-types/507f1f77bcf86cd799439011/validate",
                json=validation_data,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] is True
            assert data["data"]["valid"] is True
            assert len(data["data"]["errors"]) == 0
    
    @pytest.mark.asyncio
    async def test_validate_data_invalid(self, async_client: AsyncClient, auth_headers):
        """Test data validation with invalid data."""
        validation_data = {
            "data": {
                "user_id": "123"
                # Missing required email field
            }
        }
        
        validation_response = ValidationResponse(
            valid=False,
            errors=[{
                "field": "root",
                "message": "Required property 'email' is missing",
                "invalid_value": None
            }]
        )
        
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.validate_data.return_value = validation_response
            
            response = await async_client.post(
                "/api/v1/event-types/507f1f77bcf86cd799439011/validate",
                json=validation_data,
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] is True
            assert data["data"]["valid"] is False
            assert len(data["data"]["errors"]) == 1
            assert "email" in data["data"]["errors"][0]["message"]
    
    @pytest.mark.asyncio
    async def test_get_schema_summary_success(self, async_client: AsyncClient, auth_headers):
        """Test successful schema summary retrieval."""
        schema_summary = {
            "type": "object",
            "description": "",
            "required_fields": ["user_id", "email"],
            "optional_fields": [],
            "field_types": {
                "user_id": "string",
                "email": "string"
            }
        }
        
        with patch("app.api.v1.endpoints.event_types.get_event_type_service") as mock_service:
            mock_service_instance = AsyncMock()
            mock_service.return_value = mock_service_instance
            mock_service_instance.get_schema_summary.return_value = schema_summary
            
            response = await async_client.get(
                "/api/v1/event-types/507f1f77bcf86cd799439011/schema-summary",
                headers=auth_headers
            )
            
            assert response.status_code == status.HTTP_200_OK
            data = response.json()
            assert data["status"] is True
            assert data["data"]["type"] == "object"
            assert "user_id" in data["data"]["required_fields"]
    
    @pytest.mark.asyncio
    async def test_unauthorized_access(self, async_client: AsyncClient):
        """Test unauthorized access to endpoints."""
        response = await async_client.get("/api/v1/event-types")
        
        assert response.status_code == status.HTTP_401_UNAUTHORIZED
