import asyncio
from typing import Any, Optional, cast
from app.utils.logging import get_logger
from temporalio.client import Client, WorkflowHandle
from temporalio.worker import Worker

from app.node.node_base.node_models import WorkflowModel
from app.node.node_utils.registry import node_registry
from app.core.config import settings
from app.workflow_manager.event_workflow import EventWorkflow

logger = get_logger("services.temporal")

class TemporalService:
    """
    Service for interacting with Temporal workflows.
    """

    def __init__(self):
        """
        Initialize the TemporalService with a Temporal client.
        """
        self.client : Optional[Client] = None
        self.worker: Optional[Worker] = None
        self._worker_task: Optional[asyncio.Task] = None
        self._connected = False
        
        # Configuration from environment variables
        self.temporal_host = settings.TEMPORAL_HOST
        self.temporal_port = settings.TEMPORAL_PORT
        self.temporal_namespace = settings.TEMPORAL_NAMESPACE
        self.task_queue = "event-task-queue"

    async def connect(self):
        try:
            # Connect to Temporal server
            self.client = await Client.connect(
                f"{self.temporal_host}:{self.temporal_port}",
                namespace=self.temporal_namespace,
            )
            
            logger.info(
                "Connected to Temporal server",
                host=self.temporal_host,
                port=self.temporal_port,
                namespace=self.temporal_namespace
            )

            activity = node_registry.get_activity_node_class()
            activities = [node() for node in activity if hasattr(node, 'run')]

            # Create and start worker
            self.worker = Worker(
                self.client,
                task_queue=self.task_queue,
                workflows=[EventWorkflow],
                activities=[activity.run for activity in activities],
                
            )
            
            # Start worker in background task
            self._worker_task = asyncio.create_task(self.worker.run())
            
            # Wait a moment to ensure worker is started
            await asyncio.sleep(1)
            
            self._connected = True
            logger.info("Temporal worker started", task_queue=self.task_queue)
            
        except Exception as e:
            logger.error("Failed to start Temporal client/worker", error=str(e))
            self._connected = False
            raise

    async def disconnect(self):
        """Stop the Temporal worker and close client connection."""
        try:
            if self._worker_task and not self._worker_task.done():
                self._worker_task.cancel()
                try:
                    await self._worker_task
                except asyncio.CancelledError:
                    pass
            
            if self.worker:
                await self.worker.shutdown()
            
            self._connected = False
            logger.info("Temporal worker stopped")
            
        except Exception as e:
            logger.error("Error stopping Temporal worker", error=str(e))

    def is_connected(self) -> bool:
        """Check if the client is connected to Temporal."""
        return self._connected and self.client is not None


    async def start_workflow(self, workflow_name, *args, **kwargs):
        """
        Start a Temporal workflow.

        :param workflow_name: The name of the workflow to start.
        :param args: Positional arguments for the workflow.
        :param kwargs: Keyword arguments for the workflow.
        :return: The result of the workflow execution.
        """
        logger.info(f"Starting workflow: {workflow_name} with args: {args}, kwargs: {kwargs}")
        if self.is_connected():
            return await cast(Client, self.client).start_workflow(workflow_name, *args, **kwargs)
        else:
            raise RuntimeError("Temporal client is not connected")
    
    

    async def get_workflow_result(self, workflow_id: str) -> Any:
        """Get the result of a completed workflow."""
        if not self.is_connected():
            raise RuntimeError("Temporal client is not connected")
        else:
            handle = cast(Client,self.client).get_workflow_handle(workflow_id)
            return await handle.query("get_event_workflow_status")