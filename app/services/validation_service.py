"""
JSON Schema validation service for event type data validation.
"""

from typing import Any, Dict, List, Union
import jsonschema
from jsonschema import Draft7Validator, ValidationError as JsonSchemaValidationError

from app.schemas.event_type import ValidationError, ValidationResponse
from app.utils.exceptions import ValidationError as CerebroValidationError
from app.utils.logging import get_logger

logger = get_logger("services.validation")


class ValidationService:
    """Service for JSON schema validation with detailed error reporting."""
    
    @staticmethod
    def validate_schema_definition(schema: Dict[str, Any]) -> None:
        """
        Validate that a schema definition is a valid JSON schema.
        
        Args:
            schema: JSON schema definition to validate
            
        Raises:
            ValidationError: If schema definition is invalid
        """
        try:
            # Check if the schema is a valid JSON schema
            Draft7Validator.check_schema(schema)
            logger.debug("Schema definition validation passed")
        except jsonschema.SchemaError as e:
            logger.error(f"Invalid schema definition: {e}")
            raise CerebroValidationError(f"Invalid schema definition: {e.message}")
    
    @staticmethod
    def validate_data_against_schema(
        data: Union[Dict[str, Any], List[Any], str, int, float, bool, None],
        schema: Dict[str, Any]
    ) -> ValidationResponse:
        """
        Validate data against a JSON schema.
        
        Args:
            data: Data to validate
            schema: JSON schema to validate against
            
        Returns:
            ValidationResponse: Validation result with errors if any
        """
        try:
            # First validate the schema itself
            ValidationService.validate_schema_definition(schema)
            
            # Create validator
            validator = Draft7Validator(schema)
            
            # Collect all validation errors
            errors = []
            for error in validator.iter_errors(data):
                validation_error = ValidationService._format_validation_error(error)
                errors.append(validation_error)
            
            # Return validation response
            is_valid = len(errors) == 0
            logger.debug(f"Data validation result: valid={is_valid}, errors_count={len(errors)}")
            
            return ValidationResponse(valid=is_valid, errors=errors)
            
        except CerebroValidationError:
            # Re-raise schema validation errors
            raise
        except Exception as e:
            logger.error(f"Unexpected error during validation: {e}")
            raise CerebroValidationError(f"Validation failed: {e}")
    
    @staticmethod
    def _format_validation_error(error: JsonSchemaValidationError) -> ValidationError:
        """
        Format a jsonschema ValidationError into our custom ValidationError format.
        
        Args:
            error: jsonschema ValidationError
            
        Returns:
            ValidationError: Formatted validation error
        """
        # Build field path
        field_path = ValidationService._build_field_path(error.absolute_path)
        
        # Get the invalid value
        invalid_value = error.instance
        
        # Format error message
        message = ValidationService._format_error_message(error)
        
        return ValidationError(
            field=field_path,
            message=message,
            invalid_value=invalid_value
        )
    
    @staticmethod
    def _build_field_path(path: List[Union[str, int]]) -> str:
        """
        Build a human-readable field path from jsonschema path.
        
        Args:
            path: List of path components
            
        Returns:
            str: Formatted field path
        """
        if not path:
            return "root"
        
        path_parts = []
        for part in path:
            if isinstance(part, int):
                path_parts.append(f"[{part}]")
            else:
                if path_parts:
                    path_parts.append(f".{part}")
                else:
                    path_parts.append(str(part))
        
        return "".join(path_parts)
    
    @staticmethod
    def _format_error_message(error: JsonSchemaValidationError) -> str:
        """
        Format error message with more user-friendly descriptions.
        
        Args:
            error: jsonschema ValidationError
            
        Returns:
            str: Formatted error message
        """
        validator_name = error.validator
        
        # Custom messages for common validation errors
        if validator_name == "required":
            missing_property = error.validator_value
            return f"Required property '{missing_property}' is missing"
        
        elif validator_name == "type":
            expected_type = error.validator_value
            actual_type = type(error.instance).__name__
            return f"Expected type '{expected_type}', got '{actual_type}'"
        
        elif validator_name == "enum":
            allowed_values = error.validator_value
            return f"Value must be one of: {', '.join(map(str, allowed_values))}"
        
        elif validator_name == "minimum":
            minimum = error.validator_value
            return f"Value must be greater than or equal to {minimum}"
        
        elif validator_name == "maximum":
            maximum = error.validator_value
            return f"Value must be less than or equal to {maximum}"
        
        elif validator_name == "minLength":
            min_length = error.validator_value
            return f"String must be at least {min_length} characters long"
        
        elif validator_name == "maxLength":
            max_length = error.validator_value
            return f"String must be at most {max_length} characters long"
        
        elif validator_name == "pattern":
            pattern = error.validator_value
            return f"String does not match required pattern: {pattern}"
        
        elif validator_name == "minItems":
            min_items = error.validator_value
            return f"Array must have at least {min_items} items"
        
        elif validator_name == "maxItems":
            max_items = error.validator_value
            return f"Array must have at most {max_items} items"
        
        elif validator_name == "uniqueItems":
            return "Array items must be unique"
        
        elif validator_name == "additionalProperties":
            return "Additional properties are not allowed"
        
        elif validator_name == "format":
            format_name = error.validator_value
            return f"String does not match required format: {format_name}"
        
        else:
            # Fallback to original message
            return error.message
    
    @staticmethod
    def get_schema_summary(schema: Dict[str, Any]) -> Dict[str, Any]:
        """
        Generate a summary of schema structure for documentation purposes.
        
        Args:
            schema: JSON schema definition
            
        Returns:
            Dict[str, Any]: Schema summary
        """
        try:
            ValidationService.validate_schema_definition(schema)
            
            summary = {
                "type": schema.get("type", "unknown"),
                "description": schema.get("description", ""),
                "required_fields": [],
                "optional_fields": [],
                "field_types": {}
            }
            
            if schema.get("type") == "object":
                properties = schema.get("properties", {})
                required = schema.get("required", [])
                
                for field_name, field_schema in properties.items():
                    field_type = field_schema.get("type", "unknown")
                    summary["field_types"][field_name] = field_type
                    
                    if field_name in required:
                        summary["required_fields"].append(field_name)
                    else:
                        summary["optional_fields"].append(field_name)
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to generate schema summary: {e}")
            return {"error": f"Failed to generate summary: {e}"}
    
    @staticmethod
    def validate_and_get_detailed_report(
        data: Union[Dict[str, Any], List[Any], str, int, float, bool, None],
        schema: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Validate data and return a detailed validation report.
        
        Args:
            data: Data to validate
            schema: JSON schema to validate against
            
        Returns:
            Dict[str, Any]: Detailed validation report
        """
        validation_result = ValidationService.validate_data_against_schema(data, schema)
        schema_summary = ValidationService.get_schema_summary(schema)
        
        return {
            "validation_result": validation_result.model_dump(),
            "schema_summary": schema_summary,
            "data_type": type(data).__name__,
            "data_size": len(str(data)) if data is not None else 0
        }
