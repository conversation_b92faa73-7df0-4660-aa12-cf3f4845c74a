"""
Service for WorkFlow business logic operations.
"""

import uuid
from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession

from app.models.workflow import WorkFlow, WorkFlowVersion
from app.repositories.workflow_repository import WorkFlowRepository, WorkFlowVersionRepository
from app.repositories.tag_repository import TagRepository
from app.services.base_service import BaseService
from app.schemas.workflow import (
    WorkFlowCreate,
    WorkFlowUpdate,
    WorkFlowVersionCreate,
    WorkFlowList,
    WorkFlowFlattenedResponse,
    SimplifiedTag
)
from app.utils.exceptions import NotFoundError, ValidationError, ConflictError
from app.utils.logging import get_logger

logger = get_logger("services.workflow")


class WorkFlowService(BaseService[WorkFlow, WorkFlowCreate, WorkFlowUpdate, WorkFlowRepository]):
    """Service for WorkFlow business logic operations."""
    
    def __init__(self, db: AsyncSession):
        """
        Initialize service with database session.
        
        Args:
            db: Database session
        """
        self.db = db
        self.workflow_repository = WorkFlowRepository(db)
        self.version_repository = WorkFlowVersionRepository(db)
        self.tag_repository = TagRepository(db)
        super().__init__(self.workflow_repository)
    
    async def create_workflow(
    self,
        workflow_data: WorkFlowCreate
    ) -> WorkFlow:
        """
        Create a new workflow with its initial version in a single transaction.

        Args:
            workflow_data: Workflow creation data (may include work_flow definition)
            initial_version_data: Legacy parameter for initial version workflow definition (deprecated)

        Returns:
            WorkFlow: Created workflow with initial version

        Raises:
            ConflictError: If workflow name already exists
            ValidationError: If tag IDs are invalid
            ValidationError: If work_flow_id is provided but doesn't exist or doesn't belong to this workflow
        """
        try:
            # Check if workflow name already exists
            existing_workflow = await self.workflow_repository.get_by_name(workflow_data.name)
            if existing_workflow:
                raise ConflictError(f"Workflow with name '{workflow_data.name}' already exists")
            
            # Validate tag IDs if provided
            if workflow_data.tag_ids:
                await self._validate_tag_ids(workflow_data.tag_ids)

            # Create workflow
            workflow_dict = workflow_data.model_dump(exclude={"tag_ids", "work_flow"})
            workflow = WorkFlow(**workflow_dict)

            self.db.add(workflow)
            await self.db.flush()  # Get the UID
            await self.db.refresh(workflow)  # Ensure the UID is properly loaded

            # Handle workflow definition
            if workflow_data.work_flow:
                if not workflow.uid:
                    raise ValidationError("Failed to generate workflow UID")
    

                initial_version = WorkFlowVersion(
                    version_no=1,
                    work_flow=workflow_data.work_flow.model_dump(exclude={"version_name"}),
                    workflow_id=workflow.uid,
                    version_name=workflow_data.work_flow.version_name,
                    # version_tag_id=workflow_data.work_flow.version_tag_id,
                    created_by=workflow_data.created_by,
                    edited_by=workflow_data.created_by
                )
                self.db.add(initial_version)
                await self.db.flush()
            else:
                raise ValidationError("Workflow definition (work_flow) is required for initial version creation")

            # Set active version
            workflow.active_version_id = initial_version.id
            # Add tags if provided
            if workflow_data.tag_ids:
                await self.workflow_repository.add_tags(workflow, workflow_data.tag_ids)
            
            await self.db.commit()
            await self.db.refresh(workflow)
            
            # Load relationships
            workflow_with_relations = await self.workflow_repository.get_with_versions(workflow.uid)
            
            logger.info(
                "Workflow created with initial version",
                workflow_id=workflow.uid,
                workflow_name=workflow.name,
                created_by=workflow.created_by
            )
            
            return workflow_with_relations
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to create workflow: {str(e)}")
            raise
    
    async def get_workflow(self, workflow_id: uuid.UUID) -> WorkFlow:
        """
        Get workflow with all its versions and relationships.
        
        Args:
            workflow_id: Workflow UID
            
        Returns:
            WorkFlow: Workflow with versions and tags
            
        Raises:
            NotFoundError: If workflow not found
        """
        workflow = await self.workflow_repository.get_with_versions(workflow_id)
        if not workflow:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")
        
        return workflow
    
    async def update_workflow(
        self,
        workflow_id: uuid.UUID,
        workflow_update: WorkFlowUpdate,
        new_version_data: Optional[Dict[str, Any]] = None,
        version_tag_id: Optional[int] = None
    ) -> WorkFlow:
        """
        Update workflow and create a new version if workflow logic changes.
        
        Args:
            workflow_id: Workflow UID
            workflow_update: Workflow update data
            new_version_data: New version workflow definition
            version_tag_id: Optional tag ID for the new version
            
        Returns:
            WorkFlow: Updated workflow
            
        Raises:
            NotFoundError: If workflow not found
            ValidationError: If update data is invalid
        """
        try:
            # Get existing workflow
            workflow = await self.workflow_repository.get_with_versions(workflow_id)
            if not workflow:
                raise NotFoundError(f"Workflow with id {workflow_id} not found")
            
            # Update workflow metadata
            update_dict = workflow_update.model_dump(exclude_unset=True, exclude={"tag_ids", "work_flow"})
            for field, value in update_dict.items():
                setattr(workflow, field, value)

            # Create new version if workflow definition provided (either through work_flow field or legacy new_version_data)
            if workflow_update.work_flow or new_version_data:
                latest_version_no = await self.version_repository.get_latest_version_number(workflow_id)
                new_version_no = latest_version_no + 1

                # Use work_flow field if provided, otherwise fall back to new_version_data
                if workflow_update.work_flow:
                    version_data = WorkFlowVersionCreate(
                        version_no=new_version_no,
                        work_flow=workflow_update.work_flow.model_dump(exclude={"version_name"}),
                        version_name=workflow_update.work_flow.version_name,
                        workflow_id=workflow_id,
                        created_by=workflow_update.edited_by
                    )
                else:
                    version_data = WorkFlowVersionCreate(
                        version_no=new_version_no,
                        work_flow=new_version_data,
                        workflow_id=workflow_id,
                        version_tag_id=version_tag_id,
                        created_by=workflow_update.edited_by
                    )

                version_dict = version_data.model_dump()
                new_version = WorkFlowVersion(**version_dict)

                self.db.add(new_version)
                await self.db.flush()

                # Update active version
                workflow.active_version_id = new_version.id
            
            # Handle tag updates
            if hasattr(workflow_update, 'tag_ids') and workflow_update.tag_ids is not None:
                await self._update_workflow_tags(workflow, workflow_update.tag_ids)
            
            await self.db.commit()
            await self.db.refresh(workflow)
            
            # Reload with relationships
            updated_workflow = await self.workflow_repository.get_with_versions(workflow_id)
            
            logger.info(
                "Workflow updated",
                workflow_id=workflow_id,
                new_version_created=new_version_data is not None,
                updated_by=workflow_update.edited_by if hasattr(workflow_update, 'edited_by') else None
            )
            
            return updated_workflow
            
        except Exception as e:
            await self.db.rollback()
            logger.error(f"Failed to update workflow {workflow_id}: {str(e)}")
            raise
    
    async def get_workflows_paginated(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        active_only: bool = True
    ) -> WorkFlowList:
        """
        Get paginated list of workflows.
        
        Args:
            skip: Number of records to skip
            limit: Maximum number of records to return
            filters: Additional filters
            active_only: Whether to return only active workflows
            
        Returns:
            WorkFlowList: Paginated workflow list
        """
        if active_only:
            workflows = await self.workflow_repository.get_active_workflows(skip, limit, filters)
            total = await self.workflow_repository.count_active_workflows(filters)
        else:
            workflows = await self.workflow_repository.get_multi(skip, limit, filters)
            total = await self.workflow_repository.count(filters)
        
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return WorkFlowList(
            workflows=workflows,
            total=total,
            page=page,
            size=limit
        )
    
    async def deactivate_workflow(self, workflow_id: uuid.UUID) -> WorkFlow:
        """
        Deactivate a workflow.
        
        Args:
            workflow_id: Workflow UID
            
        Returns:
            WorkFlow: Deactivated workflow
            
        Raises:
            NotFoundError: If workflow not found
        """
        workflow = await self.workflow_repository.get_by_uid(workflow_id)
        if not workflow:
            raise NotFoundError(f"Workflow with id {workflow_id} not found")
        
        workflow.is_active = False
        await self.db.commit()
        await self.db.refresh(workflow)
        
        logger.info("Workflow deactivated", workflow_id=workflow_id)
        return workflow
    
    async def _validate_tag_ids(self, tag_ids: List[int]) -> None:
        """
        Validate that all provided tag IDs exist.
        
        Args:
            tag_ids: List of tag IDs to validate
            
        Raises:
            ValidationError: If any tag ID is invalid
        """
        for tag_id in tag_ids:
            tag_exists = await self.tag_repository.exists(tag_id)
            if not tag_exists:
                raise ValidationError(f"Tag with id {tag_id} does not exist")
    
    async def _update_workflow_tags(self, workflow: WorkFlow, new_tag_ids: List[int]) -> None:
        """
        Update workflow tags by replacing current tags with new ones.
        
        Args:
            workflow: Workflow instance
            new_tag_ids: New list of tag IDs
        """
        # Validate new tag IDs
        if new_tag_ids:
            await self._validate_tag_ids(new_tag_ids)
        
        # Get current tag IDs
        current_tag_ids = [tag.id for tag in workflow.tags]
        
        # Remove tags that are no longer needed
        tags_to_remove = [tag_id for tag_id in current_tag_ids if tag_id not in new_tag_ids]
        if tags_to_remove:
            await self.workflow_repository.remove_tags(workflow, tags_to_remove)
        
        # Add new tags
        tags_to_add = [tag_id for tag_id in new_tag_ids if tag_id not in current_tag_ids]
        if tags_to_add:
            await self.workflow_repository.add_tags(workflow, tags_to_add)

    def to_flattened_response(self, workflow: WorkFlow) -> WorkFlowFlattenedResponse:
        """
        Convert a WorkFlow object to a flattened response format.

        This method combines workflow metadata with active version data into a single
        flattened structure, making it easier for frontend consumption.

        Args:
            workflow: WorkFlow object with loaded active_version and tags relationships

        Returns:
            WorkFlowFlattenedResponse: Flattened response with combined data

        Raises:
            ValidationError: If workflow doesn't have an active version
        """
        if not workflow.active_version:
            raise ValidationError("Workflow must have an active version for flattened response")

        # Convert tags to simplified format
        simplified_tags = [
            SimplifiedTag(id=tag.id, name=tag.name)
            for tag in workflow.tags
        ]

        # Create flattened response
        return WorkFlowFlattenedResponse(
            # Workflow metadata fields
            created_at=workflow.created_at.isoformat() + "Z",
            updated_at=workflow.updated_at.isoformat() + "Z",
            name=workflow.name,
            is_active=workflow.is_active,
            description=workflow.description,
            status=workflow.status,
            created_by=workflow.created_by,
            edited_by=workflow.edited_by,
            uid=str(workflow.uid),

            # Simplified tags
            tags=simplified_tags,

            # Active version fields (flattened)
            active_version_id=workflow.active_version.id,
            version_no=workflow.active_version.version_no,
            version_name=workflow.active_version.version_name,
            work_flow=workflow.active_version.work_flow
        )
