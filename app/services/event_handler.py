"""
Event handler for processing events from AWS Lambda via RabbitMQ.
"""
from typing import Dict, Any
from app.utils.logging import get_logger

logger = get_logger(__name__)

async def process_event(event: Dict[Any, Any]) -> None:
    """
    Process events received from AWS Lambda via RabbitMQ.
    
    Args:
        event: The event payload from Lambda
    """
    logger.info(f"Processing event: {event}")
    
    # Extract relevant information from the event
    body = event.get('body', '')
    
    try:
        # Process the event based on its content
        if isinstance(body, str) and 'message' in body:
            logger.info(f"Received message event: {body}")
            # Here you can add custom processing logic
            
        # You can add more event type handlers here
        else:
            logger.info(f"Received generic event: {event}")
            
    except Exception as e:
        logger.error(f"Error processing event: {str(e)}")
        
    logger.info("Event processing completed")
