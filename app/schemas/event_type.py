"""
Event Type Pydantic schemas for request/response validation.
"""

from datetime import datetime
from typing import Any, Dict, List, Optional, Union
from uuid import UUID

from pydantic import Field, validator

from app.schemas.common import BaseSchema, PaginatedResponse


class EventTypeBase(BaseSchema):
    """Base event type schema with common fields."""
    
    name: str = Field(..., min_length=1, max_length=100, description="Unique identifier for the event type")
    description: str = Field(..., min_length=1, max_length=500, description="Human-readable description")
    schema: Dict[str, Any] = Field(..., description="JSON schema definition")
    version: int = Field(default=1, ge=1, description="Schema version number")
    
    @validator('name')
    def validate_name(cls, v):
        """Validate event type name format."""
        if not v.replace('_', '').replace('-', '').isalnum():
            raise ValueError('Name must contain only alphanumeric characters, hyphens, and underscores')
        return v.lower()
    
    @validator('schema')
    def validate_schema_structure(cls, v):
        """Basic validation of schema structure."""
        if not isinstance(v, dict):
            raise ValueError('Schema must be a valid JSON object')
        
        # Ensure required JSON schema fields
        if 'type' not in v:
            raise ValueError('Schema must have a "type" field')
        
        # Validate supported types
        supported_types = ['object', 'array', 'string', 'number', 'integer', 'boolean', 'null']
        if v.get('type') not in supported_types:
            raise ValueError(f'Schema type must be one of: {", ".join(supported_types)}')
        
        return v


class EventTypeCreate(EventTypeBase):
    """Schema for creating a new event type."""
    
    created_by: int = Field(..., description="ID of the user creating the event type")


class EventTypeUpdate(BaseSchema):
    """Schema for updating an existing event type."""
    
    name: Optional[str] = Field(None, min_length=1, max_length=100)
    description: Optional[str] = Field(None, min_length=1, max_length=500)
    schema: Optional[Dict[str, Any]] = Field(None, description="Updated JSON schema definition")
    edited_by: int = Field(..., description="ID of the user editing the event type")
    
    @validator('name')
    def validate_name(cls, v):
        """Validate event type name format."""
        if v is not None:
            if not v.replace('_', '').replace('-', '').isalnum():
                raise ValueError('Name must contain only alphanumeric characters, hyphens, and underscores')
            return v.lower()
        return v
    
    @validator('schema')
    def validate_schema_structure(cls, v):
        """Basic validation of schema structure."""
        if v is not None:
            if not isinstance(v, dict):
                raise ValueError('Schema must be a valid JSON object')
            
            # Ensure required JSON schema fields
            if 'type' not in v:
                raise ValueError('Schema must have a "type" field')
            
            # Validate supported types
            supported_types = ['object', 'array', 'string', 'number', 'integer', 'boolean', 'null']
            if v.get('type') not in supported_types:
                raise ValueError(f'Schema type must be one of: {", ".join(supported_types)}')
        
        return v


class EventType(EventTypeBase):
    """Schema for event type response."""
    
    id: str = Field(..., description="MongoDB ObjectId as string")
    created_by: int
    edited_by: Optional[int] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }


class EventTypeDetail(EventType):
    """Detailed event type schema with additional metadata."""
    
    # Additional fields can be added here for detailed view
    pass


class EventTypeList(BaseSchema):
    """Schema for event type list item."""
    
    id: str
    name: str
    description: str
    version: int
    created_by: int
    created_at: datetime
    updated_at: datetime


class ValidationRequest(BaseSchema):
    """Schema for JSON validation request."""
    
    data: Union[Dict[str, Any], List[Any], str, int, float, bool, None] = Field(
        ..., 
        description="JSON data to validate against the event type schema"
    )


class ValidationError(BaseSchema):
    """Schema for validation error details."""
    
    field: str = Field(..., description="Field path where validation failed")
    message: str = Field(..., description="Error message")
    invalid_value: Optional[Any] = Field(None, description="The invalid value that caused the error")


class ValidationResponse(BaseSchema):
    """Schema for validation response."""
    
    valid: bool = Field(..., description="Whether the data is valid")
    errors: List[ValidationError] = Field(default_factory=list, description="List of validation errors")
    
    
# Type aliases for paginated responses
EventTypeListResponse = PaginatedResponse[EventTypeList]
