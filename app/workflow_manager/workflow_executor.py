"""
Workflow Executor Module

This module contains the WorkflowExecutor class which is responsible for
executing workflow models through the Temporal workflow engine.
"""

from datetime import datetime, timedelta
from typing import List, Optional, Union
import asyncio
from app.node.node_base.node_models import NodeData, NodeStatus, WorkflowModel
from app.node.node_utils.registry import node_registry
from temporalio import workflow


class WorkflowExecutor:
   
    def __init__(self, workflow: WorkflowModel):
       self.workflow = workflow
       self.result = workflow.model_dump()

    async def run(self):
        try:
            self.result["status"] = NodeStatus.IN_PROGRESS
            self.result["start_time"] = workflow.now().isoformat()
            nodes = self.workflow.nodes.get(self.workflow.start_node)
            if nodes is None:
                raise ValueError(f"Start node '{self.workflow.start_node}' not found in workflow")
            await self._execute_next_steps([nodes])
            self.result["end_time"] = workflow.now().isoformat()
            self.result["status"] = NodeStatus.COMPLETED
            self.result["execution_time"] = self._execution_time(self.result["start_time"], self.result["end_time"])
            return self.result
        except Exception as e:
            self.result["end_time"] = workflow.now().isoformat()
            self.result["error"] = str(e)
            self.result["status"] = NodeStatus.FAILED
            self.result["execution_time"] = self._execution_time(self.result["start_time"], self.result["end_time"])
            return self.result
   
    async def _execute_next_steps(self, nodes: List[NodeData]):
        if not nodes:
            return []
        tasks = []
        for node in nodes:
            tasks.append(self._execute_node(node))
        return await asyncio.gather(*tasks, return_exceptions=True)

   
    async def _execute_node(self, node: NodeData):
        next_connection_index = await self._run_node(node)
        if next_connection_index is None or next_connection_index < 0:
                return None
        next_connection = self.workflow.connections.get(node.name)
        if not next_connection or not hasattr(next_connection, 'main') or next_connection.main is None or next_connection_index >= len(next_connection.main):
            return None
            
        next_nodes = next_connection.main[next_connection_index]
        if next_nodes is None:
            return None
            
        return await self._execute_next_steps(self._get_nodes(next_nodes))

    async def _run_node(self, node_data: NodeData) -> Optional[int]:
        node = node_data
        try:
            node_class = node_registry.get_node_class(node_data.type)
            if node_class is None:
                raise ValueError(f"Node class for type '{node_data.type}' not found in registry")
                
            node.start_time = workflow.now().isoformat()
            node.status = NodeStatus.IN_PROGRESS
            
            if getattr(node_class, 'is_activity', False):
                activity_name = f"{node_class.__name__}"
                result = await workflow.execute_activity(
                    activity_name,
                    node_data,
                    start_to_close_timeout=timedelta(seconds=30),
                )
            else:
                result = await node_class().run(node_data)

            node.end_time = workflow.now().isoformat()
            node.status = NodeStatus.COMPLETED
            node.execution_time = self._execution_time(node.start_time, node.end_time)
            node.result = result.result if hasattr(result, 'result') else None

            self.result["nodes"][node_data.name] = node.model_dump()
            return result.next_connection_index
        except Exception as e:
            node.end_time = workflow.now().isoformat()
            node.status = NodeStatus.FAILED
            node.error = str(e)
            node.execution_time = self._execution_time(node.start_time, node.end_time)
            self.result["nodes"][node_data.name] = node.model_dump()
            return None
   
    def _get_nodes(self, connection: List[str]) -> List[NodeData]:
        return [self.workflow.nodes[node_id] for node_id in connection]
    
    def _execution_time(self, start_time: Optional[str], end_time: Optional[str]) -> int:
        if not start_time or not end_time:
            return 0
        start = datetime.fromisoformat(start_time)
        end = datetime.fromisoformat(end_time)
        return int((end - start).total_seconds())

    def get_result(self):
        return self.result
