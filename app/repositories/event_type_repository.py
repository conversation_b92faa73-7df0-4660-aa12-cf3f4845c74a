"""
MongoDB repository for event type management.
"""

from datetime import datetime, timezone
from typing import Dict, List, Optional, Any
from uuid import UUID

from bson import ObjectId
from motor.motor_asyncio import AsyncIOMotorDatabase
from pymongo import ASCENDING, IndexModel
from pymongo.errors import DuplicateKeyError

from app.schemas.event_type import EventTypeCreate, EventTypeUpdate
from app.utils.exceptions import ConflictError, NotFoundError, DatabaseError
from app.utils.logging import get_logger

logger = get_logger("repositories.event_type")


class EventTypeRepository:
    """Repository for event type MongoDB operations."""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        """
        Initialize repository with MongoDB database.
        
        Args:
            db: MongoDB database instance
        """
        self.db = db
        self.collection = db.event_type
    
    async def ensure_indexes(self) -> None:
        """Create necessary indexes for optimal performance."""
        try:
            indexes = [
                IndexModel([("name", ASCENDING)], unique=True),
                IndexModel([("created_by", ASCENDING)]),
                IndexModel([("created_at", ASCENDING)]),
                IndexModel([("updated_at", ASCENDING)]),
                IndexModel([("version", ASCENDING)]),
            ]
            await self.collection.create_indexes(indexes)
            logger.info("Event type indexes created successfully")
        except Exception as e:
            logger.error(f"Failed to create indexes: {e}")
            raise DatabaseError(f"Failed to create indexes: {e}")
    
    async def create(self, event_type_data: EventTypeCreate) -> Dict[str, Any]:
        """
        Create a new event type.
        
        Args:
            event_type_data: Event type creation data
            
        Returns:
            Dict[str, Any]: Created event type document
            
        Raises:
            ConflictError: If event type name already exists
            DatabaseError: If database operation fails
        """
        try:
            now = datetime.now(timezone.utc)
            document = {
                "name": event_type_data.name,
                "description": event_type_data.description,
                "schema": event_type_data.schema,
                "version": event_type_data.version,
                "created_by": event_type_data.created_by,
                "edited_by": None,
                "created_at": now,
                "updated_at": now,
            }
            
            result = await self.collection.insert_one(document)
            
            # Retrieve the created document
            created_doc = await self.collection.find_one({"_id": result.inserted_id})
            if not created_doc:
                raise DatabaseError("Failed to retrieve created event type")
            
            # Convert ObjectId to string
            created_doc["id"] = str(created_doc["_id"])
            del created_doc["_id"]
            
            logger.info(f"Created event type: {event_type_data.name}")
            return created_doc
            
        except DuplicateKeyError:
            raise ConflictError(f"Event type with name '{event_type_data.name}' already exists")
        except Exception as e:
            logger.error(f"Failed to create event type: {e}")
            raise DatabaseError(f"Failed to create event type: {e}")
    
    async def get_by_id(self, event_type_id: str) -> Optional[Dict[str, Any]]:
        """
        Get event type by ID.
        
        Args:
            event_type_id: Event type ID (ObjectId as string)
            
        Returns:
            Optional[Dict[str, Any]]: Event type document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(event_type_id):
                return None
            
            document = await self.collection.find_one({"_id": ObjectId(event_type_id)})
            if document:
                document["id"] = str(document["_id"])
                del document["_id"]
            
            return document
            
        except Exception as e:
            logger.error(f"Failed to get event type by ID {event_type_id}: {e}")
            raise DatabaseError(f"Failed to get event type: {e}")
    
    async def get_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        Get event type by name.
        
        Args:
            name: Event type name
            
        Returns:
            Optional[Dict[str, Any]]: Event type document or None
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            document = await self.collection.find_one({"name": name})
            if document:
                document["id"] = str(document["_id"])
                del document["_id"]
            
            return document
            
        except Exception as e:
            logger.error(f"Failed to get event type by name {name}: {e}")
            raise DatabaseError(f"Failed to get event type: {e}")
    
    async def get_multi(
        self,
        skip: int = 0,
        limit: int = 100,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "created_at",
        sort_order: int = -1
    ) -> List[Dict[str, Any]]:
        """
        Get multiple event types with pagination and filtering.
        
        Args:
            skip: Number of documents to skip
            limit: Maximum number of documents to return
            filters: Optional filters to apply
            sort_by: Field to sort by
            sort_order: Sort order (1 for ascending, -1 for descending)
            
        Returns:
            List[Dict[str, Any]]: List of event type documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = filters or {}
            
            # Convert UUID filters to proper format
            if "created_by" in query and isinstance(query["created_by"], str):
                query["created_by"] = UUID(query["created_by"])
            
            cursor = self.collection.find(query).sort(sort_by, sort_order).skip(skip).limit(limit)
            documents = await cursor.to_list(length=limit)
            
            # Convert ObjectId to string for each document
            for doc in documents:
                doc["id"] = str(doc["_id"])
                del doc["_id"]
            
            return documents
            
        except Exception as e:
            logger.error(f"Failed to get event types: {e}")
            raise DatabaseError(f"Failed to get event types: {e}")
    
    async def count(self, filters: Optional[Dict[str, Any]] = None) -> int:
        """
        Count event types with optional filtering.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            int: Number of matching documents
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            query = filters or {}
            
            # Convert UUID filters to proper format
            if "created_by" in query and isinstance(query["created_by"], str):
                query["created_by"] = UUID(query["created_by"])
            
            return await self.collection.count_documents(query)
            
        except Exception as e:
            logger.error(f"Failed to count event types: {e}")
            raise DatabaseError(f"Failed to count event types: {e}")
    
    async def update(self, event_type_id: str, update_data: EventTypeUpdate) -> Optional[Dict[str, Any]]:
        """
        Update an existing event type.
        
        Args:
            event_type_id: Event type ID (ObjectId as string)
            update_data: Update data
            
        Returns:
            Optional[Dict[str, Any]]: Updated event type document or None
            
        Raises:
            ConflictError: If name conflict occurs
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(event_type_id):
                return None
            
            # Build update document
            update_doc = {"updated_at": datetime.now(timezone.utc)}
            
            if update_data.name is not None:
                update_doc["name"] = update_data.name
            if update_data.description is not None:
                update_doc["description"] = update_data.description
            if update_data.schema is not None:
                update_doc["schema"] = update_data.schema
                # Increment version when schema changes
                update_doc["$inc"] = {"version": 1}
            
            update_doc["edited_by"] = update_data.edited_by
            
            # Perform update
            result = await self.collection.find_one_and_update(
                {"_id": ObjectId(event_type_id)},
                {"$set": update_doc} if "$inc" not in update_doc else {
                    "$set": {k: v for k, v in update_doc.items() if k != "$inc"},
                    "$inc": update_doc["$inc"]
                },
                return_document=True
            )
            
            if result:
                result["id"] = str(result["_id"])
                del result["_id"]
            
            return result
            
        except DuplicateKeyError:
            raise ConflictError(f"Event type with name '{update_data.name}' already exists")
        except Exception as e:
            logger.error(f"Failed to update event type {event_type_id}: {e}")
            raise DatabaseError(f"Failed to update event type: {e}")
    
    async def delete(self, event_type_id: str) -> bool:
        """
        Delete an event type.
        
        Args:
            event_type_id: Event type ID (ObjectId as string)
            
        Returns:
            bool: True if deleted, False if not found
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(event_type_id):
                return False
            
            result = await self.collection.delete_one({"_id": ObjectId(event_type_id)})
            return result.deleted_count > 0
            
        except Exception as e:
            logger.error(f"Failed to delete event type {event_type_id}: {e}")
            raise DatabaseError(f"Failed to delete event type: {e}")
    
    async def exists(self, event_type_id: str) -> bool:
        """
        Check if event type exists.
        
        Args:
            event_type_id: Event type ID (ObjectId as string)
            
        Returns:
            bool: True if exists, False otherwise
            
        Raises:
            DatabaseError: If database operation fails
        """
        try:
            if not ObjectId.is_valid(event_type_id):
                return False
            
            count = await self.collection.count_documents({"_id": ObjectId(event_type_id)}, limit=1)
            return count > 0
            
        except Exception as e:
            logger.error(f"Failed to check event type existence {event_type_id}: {e}")
            raise DatabaseError(f"Failed to check event type existence: {e}")
