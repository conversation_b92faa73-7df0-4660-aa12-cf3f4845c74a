"""
Event Type API endpoints for event type management and validation.
"""

from typing import Any, Dict, Optional, cast
from uuid import UUID

from fastapi import APIRouter, Depends, Query, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.deps import get_current_active_user
from app.core.database import get_mongodb
from app.models.user import User
from app.repositories.event_type_repository import EventTypeRepository
from app.schemas.common import BaseResponse, PaginatedResponse
from app.schemas.event_type import (
    EventType,
    EventTypeCreate,
    EventTypeUpdate,
    EventTypeList,
    ValidationRequest,
    ValidationResponse
)
from app.services.event_type_service import EventTypeService
from app.utils.exceptions import NotFoundError, ValidationError, ConflictError
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.event_types")


async def get_event_type_service(
    db: AsyncIOMotorDatabase = Depends(get_mongodb)
) -> EventTypeService:
    """Dependency to get event type service."""
    repository = EventTypeRepository(db)
    return EventTypeService(repository)


@router.post(
    "",
    response_model=BaseResponse[EventType],
    status_code=status.HTTP_201_CREATED,
    summary="Create a new event type",
    description="Create a new event type with custom JSON schema definition"
)
async def create_event_type(
    event_type_data: EventTypeCreate,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> BaseResponse[EventType]:
    """Create a new event type."""
    try:
        # Set the creator
        event_type_data.created_by = current_user.id
        
        # Create the event type
        event_type = await service.create_event_type(event_type_data)
        
        logger.info(f"User {current_user.id} created event type: {event_type.name}")
        return BaseResponse.success(
            message="Event type created successfully",
            data=event_type
        )
        
    except ValidationError as e:
        logger.warning(f"Validation error creating event type: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error(str(e), "VALIDATION_ERROR"))
    except ConflictError as e:
        logger.warning(f"Conflict error creating event type: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error(str(e), "CONFLICT_ERROR"))
    except Exception as e:
        logger.error(f"Unexpected error creating event type: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error("Failed to create event type", "HTTP_ERROR"))

    
    


@router.get(
    "",
    response_model=BaseResponse[PaginatedResponse[EventTypeList]],
    summary="Get event types",
    description="Retrieve all event types with pagination and optional filtering"
)
async def get_event_types(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    created_by: Optional[int] = Query(None, description="Filter by creator user ID"),
    name_filter: Optional[str] = Query(None, description="Filter by name (partial match)"),
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> BaseResponse[PaginatedResponse[EventTypeList]]:
    """Get event types with pagination and filtering."""
    try:
        # Get event types
        event_types = await service.get_event_types(
            skip=skip,
            limit=limit,
            created_by=created_by,
            name_filter=name_filter
        )
        
        # Get total count
        total = await service.count_event_types(
            created_by=created_by,
            name_filter=name_filter
        )
        
        # Create paginated response
        paginated_response = PaginatedResponse.create(
            items=event_types,
            total=total,
            skip=skip,
            limit=limit
        )
        
        return BaseResponse.success(
            message="Event types retrieved successfully",
            data=paginated_response
        )
        
    except Exception as e:
        logger.error(f"Error retrieving event types: {e}")
        return cast(BaseResponse[PaginatedResponse[EventTypeList]], BaseResponse.error("Failed to retrieve event types", "HTTP_ERROR"))


@router.get(
    "/{event_type_id}",
    response_model=BaseResponse[EventType],
    summary="Get event type by ID",
    description="Retrieve a specific event type by its ID"
)
async def get_event_type(
    event_type_id: str,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> BaseResponse[EventType]:
    """Get event type by ID."""
    try:
        event_type = await service.get_event_type_by_id(event_type_id)
        
        return BaseResponse.success(
            message="Event type retrieved successfully",
            data=event_type
        )
        
    except NotFoundError as e:
        logger.warning(f"Event type not found: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error(str(e), "NOT_FOUND"))
    except Exception as e:
        logger.error(f"Error retrieving event type {event_type_id}: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error("Failed to retrieve event type", "HTTP_ERROR"))


@router.put(
    "/{event_type_id}",
    response_model=BaseResponse[EventType],
    summary="Update event type",
    description="Update an existing event type schema and metadata"
)
async def update_event_type(
    event_type_id: str,
    update_data: EventTypeUpdate,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> BaseResponse[EventType]:
    """Update an existing event type."""
    try:
        # Set the editor
        update_data.edited_by = current_user.id
        
        # Update the event type
        event_type = await service.update_event_type(event_type_id, update_data)
        
        logger.info(f"User {current_user.id} updated event type: {event_type_id}")
        return BaseResponse.success(
            message="Event type updated successfully",
            data=event_type
        )
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for update: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error(str(e), "NOT_FOUND"))
    except ValidationError as e:
        logger.warning(f"Validation error updating event type: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error(str(e), "VALIDATION_ERROR"))
    except ConflictError as e:
        logger.warning(f"Conflict error updating event type: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error(str(e), "CONFLICT_ERROR"))
    except Exception as e:
        logger.error(f"Unexpected error updating event type {event_type_id}: {e}")
        return cast(BaseResponse[EventType], BaseResponse.error("Failed to update event type", "HTTP_ERROR"))


@router.delete(
    "/{event_type_id}",
    response_model=BaseResponse[Dict[str, str]],
    summary="Delete event type",
    description="Delete an event type by ID"
)
async def delete_event_type(
    event_type_id: str,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> BaseResponse[Dict[str, str]]:
    """Delete an event type."""
    try:
        await service.delete_event_type(event_type_id)
        
        logger.info(f"User {current_user.id} deleted event type: {event_type_id}")
        return BaseResponse.success(
            message="Event type deleted successfully",
            data={"id": event_type_id}
        )
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for deletion: {e}")
        return cast(BaseResponse[Dict[str, str]], BaseResponse.error(str(e), "NOT_FOUND"))
    except Exception as e:
        logger.error(f"Error deleting event type {event_type_id}: {e}")
        return cast(BaseResponse[Dict[str, str]], BaseResponse.error("Failed to delete event type", "HTTP_ERROR"))


@router.post(
    "/{event_type_id}/validate",
    response_model=BaseResponse[ValidationResponse],
    summary="Validate data against event type schema",
    description="Validate JSON data against a specific event type schema"
)
async def validate_data(
    event_type_id: str,
    validation_request: ValidationRequest,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> BaseResponse[ValidationResponse]:
    """Validate data against event type schema."""
    try:
        validation_result = await service.validate_data(event_type_id, validation_request)
        
        message = "Data validation completed"
        if not validation_result.valid:
            message += f" with {len(validation_result.errors)} errors"
        
        return BaseResponse.success(
            message=message,
            data=validation_result
        )
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for validation: {e}")
        return cast(BaseResponse[ValidationResponse], BaseResponse.error(str(e), "NOT_FOUND"))
    except ValidationError as e:
        logger.warning(f"Schema validation error: {e}")
        return cast(BaseResponse[ValidationResponse], BaseResponse.error(str(e), "VALIDATION_ERROR"))
    except Exception as e:
        logger.error(f"Error validating data against event type {event_type_id}: {e}")
        return cast(BaseResponse[ValidationResponse], BaseResponse.error("Failed to validate data", "HTTP_ERROR"))


@router.get(
    "/{event_type_id}/schema-summary",
    response_model=BaseResponse[Dict[str, Any]],
    summary="Get event type schema summary",
    description="Get a summary of the event type schema structure"
)
async def get_schema_summary(
    event_type_id: str,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> BaseResponse[Dict[str, Any]]:
    """Get schema summary for an event type."""
    try:
        summary = await service.get_schema_summary(event_type_id)
        
        return BaseResponse.success(
            message="Schema summary retrieved successfully",
            data=summary
        )
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for schema summary: {e}")
        return cast(BaseResponse[Dict[str, Any]], BaseResponse.error(str(e), "NOT_FOUND"))
    except Exception as e:
        logger.error(f"Error getting schema summary for event type {event_type_id}: {e}")
        return cast(BaseResponse[Dict[str, Any]], BaseResponse.error("Failed to get schema summary", "HTTP_ERROR"))


@router.post(
    "/{event_type_id}/validate-detailed",
    response_model=BaseResponse[Dict[str, Any]],
    summary="Get detailed validation report",
    description="Get a detailed validation report with schema summary and data analysis"
)
async def get_detailed_validation_report(
    event_type_id: str,
    validation_request: ValidationRequest,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> BaseResponse[Dict[str, Any]]:
    """Get detailed validation report."""
    try:
        report = await service.get_detailed_validation_report(event_type_id, validation_request)
        
        return BaseResponse.success(
            message="Detailed validation report generated successfully",
            data=report
        )
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for detailed validation: {e}")
        return cast(BaseResponse[Dict[str, Any]], BaseResponse.error(str(e), "NOT_FOUND"))
    except ValidationError as e:
        logger.warning(f"Schema validation error in detailed report: {e}")
        return cast(BaseResponse[Dict[str, Any]], BaseResponse.error(str(e), "VALIDATION_ERROR"))
    except Exception as e:
        logger.error(f"Error generating detailed validation report for event type {event_type_id}: {e}")
        return cast(BaseResponse[Dict[str, Any]], BaseResponse.error("Failed to generate detailed validation report", "HTTP_ERROR"))
