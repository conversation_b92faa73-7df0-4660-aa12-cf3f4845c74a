"""
Event Type API endpoints for event type management and validation.
"""

from typing import Any, Dict, Optional, cast
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Query, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.api.deps import get_current_active_user
from app.core.database import get_mongodb
from app.models.user import User
from app.repositories.event_type_repository import EventTypeRepository
from app.schemas.common import BaseResponse, PaginatedResponse
from app.schemas.event_type import (
    EventType,
    EventTypeCreate,
    EventTypeUpdate,
    EventTypeList,
    ValidationRequest,
    ValidationResponse
)
from app.services.event_type_service import EventTypeService
from app.utils.exceptions import NotFoundError, ValidationError, ConflictError
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.event_types")


async def get_event_type_service(
    db: AsyncIOMotorDatabase = Depends(get_mongodb)
) -> EventTypeService:
    """Dependency to get event type service."""
    repository = EventTypeRepository(db)
    return EventTypeService(repository)


@router.post(
    "",
    response_model=dict[str, Any],
    status_code=status.HTTP_201_CREATED,
    summary="Create a new event type",
    description="Create a new event type with custom JSON schema definition"
)
async def create_event_type(
    event_type_data: EventTypeCreate,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> Dict[str, Any]:
    """Create a new event type."""
    try:
        
        # Create the event type
        return await service.create_event_type(event_type_data, current_user.id)
        
    except ValidationError as e:
        logger.warning(f"Validation error creating event type: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except ConflictError as e:
        logger.warning(f"Conflict error creating event type: {e}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error creating event type: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get(
    "",
    response_model=PaginatedResponse[EventTypeList],
    summary="Get event types",
    description="Retrieve all event types with pagination and optional filtering"
)
async def get_event_types(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Maximum number of records to return"),
    created_by: Optional[int] = Query(None, description="Filter by creator user ID"),
    name_filter: Optional[str] = Query(None, description="Filter by name (partial match)"),
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> PaginatedResponse[EventTypeList]:
    """Get event types with pagination and filtering."""
    try:
        # Get event types
        event_types = await service.get_event_types(
            skip=skip,
            limit=limit,
            created_by=created_by,
            name_filter=name_filter
        )
        
        # Get total count
        total = await service.count_event_types(
            created_by=created_by,
            name_filter=name_filter
        )
        
        # Create paginated response
        return PaginatedResponse.create(
            items=event_types,
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"Error retrieving event types: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.get(
    "/{event_type_id}",
    response_model=Dict[str, Any],
    summary="Get event type by ID",
    description="Retrieve a specific event type by its ID"
)
async def get_event_type(
    event_type_id: str,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> Dict[str, Any]:
    """Get event type by ID."""
    try:
        return await service.get_event_type_by_id(event_type_id)
        
    except NotFoundError as e:
        logger.warning(f"Event type not found: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error retrieving event type {event_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.put(
    "/{event_type_id}",
    response_model=EventType,
    summary="Update event type",
    description="Update an existing event type schema and metadata"
)
async def update_event_type(
    event_type_id: str,
    update_data: EventTypeUpdate,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> dict[str, Any]:
    """Update an existing event type."""
    try:
        
        # Update the event type
        return await service.update_event_type(event_type_id, update_data, current_user.id)
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for update: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        logger.warning(f"Validation error updating event type: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except ConflictError as e:
        logger.warning(f"Conflict error updating event type: {e}")
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Unexpected error updating event type {event_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )

@router.delete(
    "/{event_type_id}",
    response_model=bool,
    summary="Delete event type",
    description="Delete an event type by ID"
)
async def delete_event_type(
    event_type_id: str,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> bool:
    """Delete an event type."""
    try:
        return await service.delete_event_type(event_type_id)
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for deletion: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error deleting event type {event_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post(
    "/{event_type_id}/validate",
    response_model=ValidationResponse,
    summary="Validate data against event type schema",
    description="Validate JSON data against a specific event type schema"
)
async def validate_data(
    event_type_id: str,
    validation_request: ValidationRequest,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> ValidationResponse:
    """Validate data against event type schema."""
    try:
        return await service.validate_data(event_type_id, validation_request)
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for validation: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        logger.warning(f"Schema validation error: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error validating data against event type {event_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.get(
    "/{event_type_id}/schema-summary",
    response_model=Dict[str, Any],
    summary="Get event type schema summary",
    description="Get a summary of the event type schema structure"
)
async def get_schema_summary(
    event_type_id: str,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> Dict[str, Any]:
    """Get schema summary for an event type."""
    try:
        return await service.get_schema_summary(event_type_id)
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for schema summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error getting schema summary for event type {event_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )


@router.post(
    "/{event_type_id}/validate-detailed",
    response_model=Dict[str, Any],
    summary="Get detailed validation report",
    description="Get a detailed validation report with schema summary and data analysis"
)
async def get_detailed_validation_report(
    event_type_id: str,
    validation_request: ValidationRequest,
    current_user: User = Depends(get_current_active_user),
    service: EventTypeService = Depends(get_event_type_service)
) -> Dict[str, Any]:
    """Get detailed validation report."""
    try:
        return await service.get_detailed_validation_report(event_type_id, validation_request) 
        
    except NotFoundError as e:
        logger.warning(f"Event type not found for detailed validation: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except ValidationError as e:
        logger.warning(f"Schema validation error in detailed report: {e}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error generating detailed validation report for event type {event_type_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e)
        )
