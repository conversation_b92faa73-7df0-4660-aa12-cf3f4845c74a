"""
WorkFlow API endpoints for workflow management.
"""

import uuid
from typing import Dict, Any, Optional
from fastapi import APIRouter, Depends, status, Query
from sqlalchemy.ext.asyncio import AsyncSession

from app.api.deps import get_current_active_user
from app.core.database import get_db
from app.models.user import User
from app.schemas.common import PaginationParams, BaseResponse
from app.schemas.workflow import (
    WorkFlowCreate,
    WorkFlowUpdate,
    WorkFlow as WorkFlowSchema,
    WorkFlowDetail,
    WorkFlowFlattenedResponse,
    SimplifiedTag,
    WorkFlowList
)
from app.services.workflow_service import WorkFlowService
from app.utils.exceptions import (
    NotFoundError,
    ValidationError,
    ConflictError
)
from app.utils.logging import get_logger

router = APIRouter()
logger = get_logger("api.workflows")


@router.post("/", response_model=BaseResponse[WorkFlowFlattenedResponse], status_code=status.HTTP_201_CREATED)
async def create_workflow(
    workflow_data: WorkFlowCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> BaseResponse[WorkFlowFlattenedResponse]:
    """
    Create a new workflow with its initial version.

    This endpoint creates both a WorkFlow and its initial WorkFlowVersion in a single transaction.
    The workflow metadata and version-specific data are handled through the work_flow field
    in the WorkFlowCreate schema.

    The work_flow field supports:
    - Creating new versions (when work_flow_id is not provided)
    - Updating existing versions (when work_flow_id is provided)

    Args:
        workflow_data: Workflow creation data including metadata, tags, and optional work_flow definition
        current_user: Current authenticated user
        db: Database session

    Returns:
        BaseResponse[WorkFlowFlattenedResponse]: Standardized response with flattened workflow data

    Raises:
        ConflictError: If workflow name already exists
        ValidationError: If tag IDs are invalid or work_flow_id doesn't exist/belong to workflow
    """
    try:
        # Set created_by from current user
        workflow_data.created_by = current_user.id
        
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.create_workflow(
            workflow_data
        )
        
        logger.info(
            "Workflow created via API",
            workflow_id=workflow.uid,
            workflow_name=workflow.name,
            created_by=current_user.id
        )

        # Convert to flattened response format
        flattened_response = workflow_service.to_flattened_response(workflow)

        return BaseResponse.success(
            message="Resource created successfully",
            data=flattened_response
        )
    
    except ConflictError as e:
        logger.warning(f"Workflow creation conflict: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Workflow creation validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error creating workflow: {str(e)}")
        raise


@router.get("/{workflow_id}", response_model=BaseResponse[WorkFlowFlattenedResponse], status_code=status.HTTP_200_OK)
async def get_workflow(
    workflow_id: uuid.UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> BaseResponse[WorkFlowFlattenedResponse]:
    """
    Get a workflow by ID with flattened structure combining workflow and active version data.

    This endpoint retrieves a workflow with a flattened response structure that combines:
    - Workflow metadata (uid, name, is_active, created_by, etc.)
    - Active version data (id, version_no, work_flow definition)
    - Associated tags
    - Complete workflow definition with nodes and connections

    Args:
        workflow_id: Workflow UID
        current_user: Current authenticated user
        db: Database session

    Returns:
        BaseResponse[WorkFlowFlattenedResponse]: Standardized response with flattened workflow data

    Raises:
        NotFoundError: If workflow not found or has no active version
    """
    try:
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.get_workflow(workflow_id)

        # Find the active version
        active_version = None
        if workflow.active_version_id:
            active_version = next(
                (v for v in workflow.versions if v.id == workflow.active_version_id),
                None
            )

        if not active_version:
            raise NotFoundError(f"Workflow {workflow_id} has no active version")

        logger.info(
            "Workflow retrieved via API",
            workflow_id=workflow_id,
            active_version_id=active_version.id,
            requested_by=current_user.id
        )

        # Create simplified tags (only id and name)
        simplified_tags = [
            SimplifiedTag(id=tag.id, name=tag.name)
            for tag in workflow.tags
        ]

        # Create flattened response combining workflow and active version data
        flattened_data = WorkFlowFlattenedResponse(
            # Workflow fields
            created_at=workflow.created_at.isoformat() + "Z",
            updated_at=workflow.updated_at.isoformat() + "Z",
            name=workflow.name,
            is_active=workflow.is_active,
            description=workflow.description,
            status=workflow.status.value,
            created_by=workflow.created_by,
            edited_by=workflow.edited_by,
            uid=str(workflow.uid),
            tags=simplified_tags,

            # Active version fields (renamed id to active_version_id)
            active_version_id=active_version.id,
            version_no=active_version.version_no,
            version_name=active_version.version_name,
            work_flow=active_version.work_flow
        )

        return BaseResponse.success(
            message="Workflow retrieved successfully",
            data=flattened_data
        )
            
    except NotFoundError as e:
        logger.warning(f"Workflow not found: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving workflow {workflow_id}: {str(e)}")
        raise


@router.get("/", response_model=WorkFlowList, status_code=status.HTTP_200_OK)
async def get_workflows(
    pagination: PaginationParams = Depends(),
    active_only: bool = Query(True, description="Filter to active workflows only"),
    created_by: Optional[int] = Query(None, description="Filter by creator user ID"),
    tag_ids: Optional[str] = Query(None, description="Comma-separated tag IDs to filter by"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowList:
    """
    Get paginated list of workflows with filtering options.
    
    This endpoint supports:
    - Pagination with skip/limit
    - Filtering by active status
    - Filtering by creator
    - Filtering by associated tags
    
    Args:
        pagination: Pagination parameters (skip, limit)
        active_only: Whether to return only active workflows
        created_by: Filter by creator user ID
        tag_ids: Comma-separated tag IDs to filter by
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        WorkFlowList: Paginated workflow list with metadata
    """
    try:
        # Parse tag IDs if provided
        filters = {}
        if created_by:
            filters["created_by"] = created_by
        if tag_ids:
            try:
                tag_id_list = [int(tag_id.strip()) for tag_id in tag_ids.split(",")]
                filters["tag_ids"] = tag_id_list
            except ValueError:
                raise ValidationError("Invalid tag IDs format. Use comma-separated integers.")
        
        workflow_service = WorkFlowService(db)
        workflow_list = await workflow_service.get_workflows_paginated(
            skip=pagination.skip,
            limit=pagination.limit,
            filters=filters,
            active_only=active_only
        )
        
        logger.info(
            "Workflows list retrieved via API",
            total_count=workflow_list.total,
            page=workflow_list.page,
            requested_by=current_user.id
        )
        
        return workflow_list
        
    except ValidationError as e:
        logger.warning(f"Workflow list validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error retrieving workflows: {str(e)}")
        raise


@router.put("/{workflow_id}", response_model=WorkFlowDetail, status_code=status.HTTP_200_OK)
async def update_workflow(
    workflow_id: uuid.UUID,
    workflow_update: WorkFlowUpdate,
    new_version_data: Optional[Dict[str, Any]] = None,
    version_tag_id: Optional[int] = Query(None, description="Tag ID for the new version"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowDetail:
    """
    Update a workflow and optionally create a new version.
    
    This endpoint handles:
    - Workflow metadata updates
    - Creating new versions when workflow logic changes
    - Maintaining version history integrity
    - Tag management
    
    Args:
        workflow_id: Workflow UID
        workflow_update: Workflow update data
        new_version_data: Optional new version workflow definition
        version_tag_id: Optional tag ID for the new version
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        WorkFlowDetail: Updated workflow with version information
        
    Raises:
        NotFoundError: If workflow not found
        ValidationError: If update data is invalid
    """
    try:
        # Set edited_by from current user
        workflow_update.edited_by = current_user.id
        
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.update_workflow(
            workflow_id=workflow_id,
            workflow_update=workflow_update,
            new_version_data=new_version_data,
            version_tag_id=version_tag_id
        )
        
        logger.info(
            "Workflow updated via API",
            workflow_id=workflow_id,
            new_version_created=new_version_data is not None,
            updated_by=current_user.id
        )
        
        return WorkFlowDetail.model_validate(workflow)
        
    except NotFoundError as e:
        logger.warning(f"Workflow update failed - not found: {str(e)}")
        raise
    except ValidationError as e:
        logger.warning(f"Workflow update validation error: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error updating workflow {workflow_id}: {str(e)}")
        raise


@router.patch("/{workflow_id}/deactivate", response_model=WorkFlowSchema, status_code=status.HTTP_200_OK)
async def deactivate_workflow(
    workflow_id: uuid.UUID,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> WorkFlowSchema:
    """
    Deactivate a workflow.
    
    This endpoint sets the workflow's is_active flag to False,
    effectively removing it from active workflow lists.
    
    Args:
        workflow_id: Workflow UID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        WorkFlowSchema: Deactivated workflow
        
    Raises:
        NotFoundError: If workflow not found
    """
    try:
        workflow_service = WorkFlowService(db)
        workflow = await workflow_service.deactivate_workflow(workflow_id)
        
        logger.info(
            "Workflow deactivated via API",
            workflow_id=workflow_id,
            deactivated_by=current_user.id
        )
        
        return WorkFlowSchema.model_validate(workflow)
        
    except NotFoundError as e:
        logger.warning(f"Workflow deactivation failed - not found: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error deactivating workflow {workflow_id}: {str(e)}")
        raise
